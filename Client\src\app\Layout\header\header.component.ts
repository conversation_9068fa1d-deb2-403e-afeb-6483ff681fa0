import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { AuthService } from '../../Core/Services/auth.service';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import {
  NotificationService,
  NotificationGroup,
  Notification,
} from '../../Core/Services/notification.service';
import { UserAvatarService } from '../../Core/Services/user-avatar.service';
import { Subscription } from 'rxjs';

@Component({
  standalone: false,
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy {
  userAvatar: string = '';
  userName: string = '';
  userEmail: string = '';
  userRole: string = '';
  showHeader: boolean = true;
  isGlobalAdmin: boolean = false;
  showNotifications: boolean = false;
  notificationGroups: NotificationGroup[] = [];
  notifications: Notification[] = [];
  unreadCount: number = 0;
  isLoadingNotifications: boolean = false;
  private avatarSubscription: Subscription | null = null;
  private notificationSubscriptions: Subscription[] = [];

  router = inject(Router);

  constructor(
    private authservice: AuthService,
    private notificationService: NotificationService,
    private messageService: MessageService,
    private userAvatarService: UserAvatarService,
  ) {}

  ngOnInit(): void {
    this.avatarSubscription = this.userAvatarService
      .getUserAvatar()
      .subscribe((avatar) => {
        this.userAvatar = avatar;
      });

    const userInfo = this.authservice.getUserInfo();
    this.userName = userInfo?.name || '';
    this.userEmail = userInfo?.email || '';

    // Check if user has Global Admin role
    if (userInfo && userInfo.role) {
      this.userRole = Array.isArray(userInfo.role)
        ? userInfo.role[0]
        : userInfo.role;
      this.isGlobalAdmin = Array.isArray(userInfo.role)
        ? userInfo.role.includes('Global Admin')
        : userInfo.role === 'Global Admin';
    }

    this.router.events.subscribe(() => {
      this.showHeader =
        !this.router.url.includes('/auth/login') &&
        !this.router.url.includes('/auth/VerifyOtp');

      // Refresh user role on navigation
      if (this.authservice.isLoggedIn()) {
        const updatedUserInfo = this.authservice.getUserInfo();
        if (updatedUserInfo && updatedUserInfo.role) {
          this.userRole = Array.isArray(updatedUserInfo.role)
            ? updatedUserInfo.role[0]
            : updatedUserInfo.role;
          this.isGlobalAdmin = Array.isArray(updatedUserInfo.role)
            ? updatedUserInfo.role.includes('Global Admin')
            : updatedUserInfo.role === 'Global Admin';
        }
      }
    });

    // Subscribe to notification service observables with proper subscription management
    this.notificationSubscriptions.push(
      this.notificationService.getNotificationGroups().subscribe((groups) => {
        this.notificationGroups = groups;
      }),
    );

    this.notificationSubscriptions.push(
      this.notificationService.getNotifications().subscribe((notifications) => {
        this.notifications = notifications;
      }),
    );

    this.notificationSubscriptions.push(
      this.notificationService.getUnreadCount().subscribe((count) => {
        this.unreadCount = count;
      }),
    );

    this.notificationSubscriptions.push(
      this.notificationService.getIsLoading().subscribe((isLoading) => {
        this.isLoadingNotifications = isLoading;
      }),
    );

    // Initialize notification service to load notifications automatically
    // This will load the notification count immediately when the header loads
    if (this.authservice.isLoggedIn() && this.showHeader) {
      this.notificationService.initialize();
    }
  }

  toggleDropdown() {
    const dropdown = document.querySelector('.dropdown') as HTMLElement;
    dropdown.classList.toggle('active');
  }

  toggleNotifications() {
    this.showNotifications = !this.showNotifications;

    if (this.showNotifications) {
      // Mark notifications as read when panel is opened
      this.notificationService.markAllAsRead();
    }
  }

  closeNotifications(event: MouseEvent) {
    // Close notifications panel when clicking outside
    if (this.showNotifications) {
      const target = event.target as HTMLElement;
      const notificationPanel = document.querySelector(
        '.notification-panel',
      ) as HTMLElement;
      const bellButton = document.querySelector('.bell-button') as HTMLElement;

      if (
        notificationPanel &&
        !notificationPanel.contains(target) &&
        bellButton &&
        !bellButton.contains(target)
      ) {
        this.showNotifications = false;
      }
    }
  }

  navigateToEvent(eventId: number) {
    this.showNotifications = false;
    this.router.navigate(['/events/event-details', eventId]);
  }

  logout() {
    this.authservice.logout();
    this.messageService.add({
      severity: 'success',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out',
      life: 3000,
    });
    this.router.navigate(['/auth/login']);
  }

  ngOnDestroy(): void {
    // Clean up avatar subscription when component is destroyed
    if (this.avatarSubscription) {
      this.avatarSubscription.unsubscribe();
      this.avatarSubscription = null;
    }

    // Clean up all notification subscriptions
    this.notificationSubscriptions.forEach((subscription) => {
      if (subscription) {
        subscription.unsubscribe();
      }
    });
    this.notificationSubscriptions = [];
  }
}
